package com.czur.starry.device.wallpaperdisplay.view.fragment

import FlexboxAdapter
import TemplateAdapter
import android.annotation.SuppressLint
import android.os.Bundle
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.download.startDownload
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.isValidImage
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.bean.CustomImageEntity
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.bean.FileMode
import com.czur.starry.device.wallpaperdisplay.databinding.FragmentHomeBinding
import com.czur.starry.device.wallpaperdisplay.util.CUSTOM_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.RECENT_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.SpacesItemDecoration
import com.czur.starry.device.wallpaperdisplay.util.addDataLocal
import com.czur.starry.device.wallpaperdisplay.util.getAllData
import com.czur.starry.device.wallpaperdisplay.util.getCurrentTagWithData
import com.czur.starry.device.wallpaperdisplay.util.getHasEmptyCustomData
import com.czur.starry.device.wallpaperdisplay.util.getName
import com.czur.starry.device.wallpaperdisplay.util.getRecentData
import com.czur.starry.device.wallpaperdisplay.util.getSystemProp
import com.czur.starry.device.wallpaperdisplay.util.getValueToList
import com.czur.starry.device.wallpaperdisplay.util.setSystemProp
import com.czur.starry.device.wallpaperdisplay.view.activity.PreviewActivity
import com.czur.starry.device.wallpaperdisplay.view.dialog.LocalSelectDialog
import com.czur.starry.device.wallpaperdisplay.view.dialog.RenameDialog
import com.czur.starry.device.wallpaperdisplay.view.dialog.ScanQRCodeDialog
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM.Companion.rootDir
import com.czur.starry.device.wallpaperdisplay.view.vm.PhotoVM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * created by wangh 22.0728
 */

private const val TAG = "HomeFragment"
private const val TEMPLATE_MAX_COUNT = 20  //自定义模板最大数

class HomeFragment : CZViewBindingFragment<FragmentHomeBinding>() {

    //显示图片加载
    private val photoVM: PhotoVM by viewModels({ requireActivity() })
    //本地图片选择
    private val displayVM: DisplayVM by viewModels({ requireActivity() })

    private val recentAdapter = TemplateAdapter()
    private val customAdapter = FlexboxAdapter()
    private val meetAdapter = TemplateAdapter()

    private var dialog: LocalSelectDialog? = null
    private var dialogQrcode: ScanQRCodeDialog? = null

    //定制图片加载完成
    private var initFinish = AtomicBoolean(false)

    //是否正在处理图片（扫码）
    private var isGettingPic = AtomicBoolean(false)
    //是否正在接收扫码图片
    private var isReceiving = AtomicBoolean(false)

    //循环遍历未接受图片
    private var queryImageJob: Job? = null
    private val uploadImageMutex = Mutex()

    private var lastUploadToastTime: Long = 0L

    private lateinit var titlesArray: Array<String>
    private var currentClickTag:Int = 0


    override fun FragmentHomeBinding.initBindingViews() {
        //本地图片选择
        displayVM.shareFileLive.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            logTagD(TAG, "=====it=$it")
            val srcFile = FileEntity(
                absPath = it.absPath,
                fileType = it.fileType,
                name = it.name,
                belongTo = it.belongTo
            )
            val imgFile = File(srcFile.absPath)
            launch {
                if (imgFile.isValidImage()) {
                    delay(200)
                    //确认添加本地后刷新
                    showLocalDialog(srcFile)
                } else {
                    toast(R.string.toast_wallpaper_upload_damaged_image)
                }
            }

        }

        //扫码上传
        photoVM.unReceivedImageLive.observe(viewLifecycleOwner) {
            lifecycleScope.launch {
                uploadImageMutex.withLock {
                    isReceiving.set(true)
                    delay(500)
                    for (i in 0 until it.size) {
                        while (isGettingPic.get()) delay(200)
                        getImageFromUrl(it.get(i))
                    }
                    isReceiving.set(false)
                }
            }
        }

        //最近使用
        recycleRecent.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (pos < 0) return@doOnItemClick false

            when (view.id) {
                R.id.tv_stop -> {
                    (vh.itemView.findViewById(R.id.tv_stop) as TextView).gone()
                    (vh.itemView.findViewById(R.id.tv_play_time) as TextView).gone()
                    launch {
                        setSystemProp(false)
                        recentAdapter.modifyData()
                        recentAdapter.cancelTimeCount()
                    }
                    true
                }

                R.id.tv_name -> {
                    val entity = recentAdapter.getData(pos)
                    entity.wallPaperEntity?.let { doRenameFile(RECENT_ASSETS, it, pos) }
                    true
                }

                else -> {
                    val data = recentAdapter.getData(pos)
                    //由于测试偶现ota升级后点击进入状态不对，版本临时加个保险。
                    var isPlay = false
                    if (pos == 0 && getSystemProp()) {
                        isPlay = true
                    }
                    data.wallPaperEntity?.let {
                        PreviewActivity.startWithFilePath(
                            requireContext(),
                            it.copy(),
                            FileMode.RECENT,
                            isPlay
                        )
                    }
                    true
                }
            }
        }

        //会议模板
        meetList.doOnItemClick(true) { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (pos < 0) return@doOnItemClick false

            when (view.id) {
                R.id.im_local,
                R.id.tv_local,
                R.id.layout_local -> {
                    uploadFromLocal()
                    true
                }

                R.id.im_qrcode,
                R.id.tv_qrcode,
                R.id.layout_qrcode -> {
                    uploadFromQRCode()
                    true
                }

                R.id.tv_name -> {
                    logTagD(TAG,"===currentClickTag==$currentClickTag, ===pos==$pos")

                    val entity = if (currentClickTag == 0) {
                        meetAdapter.getData(pos)
                    } else {
                        customAdapter.getData(pos)
                    }
                    if (entity.fileType == FileMode.CUSTOM) {
                        entity.wallPaperEntity?.let { doRenameFile(CUSTOM_ASSETS, it, pos) }
                    } else {
                        toast(R.string.toast_wallpaper_rename_tip)
                    }
                    true
                }

                else -> {
                    val titleCount = titlesArray.size - 1
                    if (currentClickTag == titleCount && pos == 0) {
                        return@doOnItemClick true
                    }

                    val itemEntity = if (currentClickTag == titleCount) {
                        customAdapter.getData(pos)
                    } else {
                        meetAdapter.getData(pos)
                    }
                    itemEntity.wallPaperEntity?.let { it ->
                        PreviewActivity.startWithFilePath(
                            requireContext(),
                            it.copy(),
                            itemEntity.fileType
                        )
                    }
                    true
                }
            }

        }

        photoVM.isShowRecent.observe(viewLifecycleOwner) {
            if (!it) { //是否显示recent
                tvTitleRecent.gone()
                recentDriver.gone()
            } else {
                tvTitleRecent.show()
                recentDriver.show()
            }
        }

        initSelectBar()
    }



    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            //加载模板
            initRecent()
            initFixedAndCustom()
        }
    }

    // 初始化过滤选择条
    private fun initSelectBar() {
        titlesArray = resources.getStringArray(R.array.select_bar_items)
        binding.selectBar.setTitles(titlesArray)
        binding.selectBar.setOnSelChangeListener { selPos ->
            launch {
                currentClickTag = selPos
                updateMeetingList()
                delay(100L)
                updateUI()
            }
        }
        updateMeetingList()
    }

    private fun updateUI() {
        launch {
            if (currentClickTag < titlesArray.size - 1){
                binding.meetList.adapter = meetAdapter
            } else {
                binding.meetList.adapter = customAdapter
            }
        }
    }

    private fun updateMeetingList() {
        launch {
            if (currentClickTag < titlesArray.size - 1){
                meetAdapter.refreshData(getCurrentTagWithData(currentClickTag), FileMode.MEETING)
            } else {
                customAdapter.refreshData(getHasEmptyCustomData())
            }
        }
    }

    private fun updateRecent() {
        launch {
            photoVM.refreshIsShow()
            recentAdapter.refreshData(getRecentData())
        }
    }

    private suspend fun initRecent() = withContext(Dispatchers.Main) {
        //最近使用
        binding.recycleRecent.layoutManager = GridLayoutManager(context, 4)
        binding.recycleRecent.addItemDecoration(SpacesItemDecoration())

        photoVM.refreshIsShow()
        recentAdapter.setData(requireContext(), getRecentData())
        binding.recycleRecent.adapter = recentAdapter
        binding.recycleRecent.closeDefChangeAnimations()
    }

    private suspend fun initFixedAndCustom() = withContext(Dispatchers.Main) {
        //加载屏保模板
        binding.meetList.layoutManager = GridLayoutManager(context, 4)
        binding.meetList.addItemDecoration(SpacesItemDecoration())
        binding.meetList.itemAnimator = null

        val meetList = getAllData()
        meetAdapter.setData(requireContext(), meetList, FileMode.MEETING)
        binding.meetList.adapter = meetAdapter
        binding.meetList.closeDefChangeAnimations()

        customAdapter.setData(requireContext(), getHasEmptyCustomData())
        initFinish.set(true)
    }

    //显示文件选择
    private fun showFileFragment() {
        displayVM.setFileType(FileType.IMAGE)
        FileChooseFloatFragment().show()
        dialog?.dismiss()
    }

    //本地图片上传
    private fun uploadFromLocal() {
        launch {
            val list = getValueToList(CUSTOM_ASSETS)
            if (list.size >= TEMPLATE_MAX_COUNT) {
                val currentTime = System.currentTimeMillis()
                // 如果距离上次显示已经超过 4 秒，则显示 toast
                if (currentTime - lastUploadToastTime > 4000) {
                    toast(R.string.str_create_temp_filed)
                    lastUploadToastTime = currentTime
                }
            } else {
                //本地上传
                showLocalDialog()
            }
        }
    }

    //二维码扫描上传
    private fun uploadFromQRCode() {
        launch {
            val list = getValueToList(CUSTOM_ASSETS)
            if (list.size >= TEMPLATE_MAX_COUNT) {
                val currentTime = System.currentTimeMillis()
                // 如果距离上次显示已经超过 4 秒，则显示 toast
                if (currentTime - lastUploadToastTime > 4000) {
                    toast(R.string.str_create_temp_filed)
                    lastUploadToastTime = currentTime
                }
            } else if (!photoVM.isNetworking.get()) {
                toast(R.string.str_net_not_work)
            } else {
                //扫码上传
                showQRCodeDialog()
            }
        }
    }

    //文件重命名
    @SuppressLint("StringFormatInvalid")
    private fun doRenameFile(tag: String, entity: FileEntity, pos: Int) {
        RenameDialog(
            inputText = entity.name.substringBeforeLast("."),
        ) {
            logTagD(TAG,"===newName.name==$it")
            when {
                // 文件名没有变化时,不做任何操作
                it == entity.name.substringBeforeLast(".") -> {
                    logTagI(TAG, "文件名没有改变")
                }
                it.isNotBlank() -> {
                    launch {
                        if (tag == CUSTOM_ASSETS) {
                            if (hasName(it)) {
                                SingleBtnCommonFloat(
                                    title = getString(R.string.dialog_rename_tip),
                                    content = getString(R.string.dialog_rename_has_name,it),
                                    confirmBtnText = getString(R.string.btn_tv_ok)
                                ) { dialog ->
                                    dialog.dismiss()
                                }.show()
                                return@launch
                            }
                        }

                        //数据更新
                        val result = photoVM.updateDataName(tag, entity,it)
                        //UI更新
                        entity.name = it
                        if (result) {
                            logTagD(TAG,"===rename.result==$it")
                            if (tag == CUSTOM_ASSETS) {
                                val textView =
                                    binding.meetList.layoutManager?.findViewByPosition(pos)
                                        ?.findViewById<TextView>(R.id.tv_name)
                                textView?.text = it
                            } else {
                                val textView =
                                    binding.recycleRecent.layoutManager?.findViewByPosition(pos)
                                        ?.findViewById<TextView>(R.id.tv_name)
                                textView?.text = it
                            }
                        }
                    }
                }

            }
        }.show()
    }

    private fun hasName(name: String): Boolean {
        if (currentClickTag == 0) {
            meetAdapter.getAllData()?.forEach { entity ->
                if (name == entity.wallPaperEntity?.name?.substringBeforeLast(".")) {
                    return true
                }
            }
        } else {
            customAdapter.getAllData().forEach { entity ->
                if (name == entity.wallPaperEntity?.name?.substringBeforeLast(".")) {
                    return true
                }
            }
        }
        return false
    }

    //本地上传dialog
    private fun showLocalDialog(it: FileEntity = FileEntity()) {
        launch {
            if (dialog?.isVisible == true) {
               return@launch
            }

            dialog = LocalSelectDialog.Builder()
                .setSelectClickListener {
                    showFileFragment()
                }.setConfirmClickListener {

                    if (it.absPath == "") return@setConfirmClickListener
                    //复制图片
                    doCopyTask(it)
                }.setImageName(requireContext(), it.name)
                .setImageView(requireContext(), it.absPath)
                .build()
            dialog?.show()
        }
    }

    //扫码上传dialog
    private fun showQRCodeDialog() {
        launch {
            val url = photoVM.getUploadUrl()
            if (url == null) {
                toast(R.string.str_geturl_failed)
                return@launch
            }
            if (dialogQrcode?.isVisible == true) {
                return@launch
            }
            val qrCode = url.toString() + Constants.SERIAL
            dialogQrcode = ScanQRCodeDialog.Builder()
                .setSelectClickListener {
                    showFileFragment()
                }.setConfirmClickListener {
                    dialogQrcode?.dismiss()
                    dialogQrcode = null
                }.setLink(qrCode)
                .build()
            dialogQrcode?.show()
        }
    }

    private fun doCopyTask(it: FileEntity = FileEntity()) {
        launch {
            uploadImageMutex.withLock {
                delay(500)
                if (customAdapter.itemCount > TEMPLATE_MAX_COUNT) {
                    toast(R.string.str_create_temp_filed)
                } else {
                    try {
                        val targetDir = photoVM.copyToLocal(it)
                        addDataLocal(targetDir)
                        val data = getHasEmptyCustomData()
                        customAdapter.refreshData(data)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        toast(R.string.toast_wallpaper_upload_damaged_image)
                    }
                }
            }
            displayVM.clearShareFile()
        }
    }

    //扫码下载
    private suspend fun getImageFromUrl(entity: CustomImageEntity) = withContext(Dispatchers.IO) {
        logTagD(TAG, "=======getImageFromUrl=$entity")
        isGettingPic.set(true)
        if (customAdapter.itemCount > TEMPLATE_MAX_COUNT) {
            lifecycleScope.launch {
                val id = entity.id
                photoVM.responseReceived(id)
                toast(R.string.str_create_temp_filed)
                dialogQrcode?.dismiss()
                isGettingPic.set(false)
            }
        } else {
            val url = entity.directory
            val id = entity.id
            var imageFormat = getString(R.string.jpg)
            if (url.contains(getString(R.string.png))) {
                imageFormat = getString(R.string.png)
            }
            val list = getValueToList(CUSTOM_ASSETS)
            val name = getName(list)
            val path = name + imageFormat
            val desFile = File(rootDir, path)
            photoVM.mkRootDir()
            if (startDownload(url, desFile)) {

                val desc =
                    FileEntity(
                        absPath = desFile.absolutePath,
                        name = name,
                        fileType = FileType.IMAGE
                    )
                logTagD(TAG,"=====desc=absPath=${desc.absPath}")
                logTagD(TAG,"=====desc=name=${desc.name}")
                lifecycleScope.launch {
                    addDataLocal(desc)
                    val data = getHasEmptyCustomData()
                    customAdapter.refreshData(data)
                    toast(R.string.str_create_temp_success1)
                    photoVM.responseReceived(id)
                    isGettingPic.set(false)
                }
            } else {
                isGettingPic.set(false)
                logTagD(TAG, "===图片下载失败=")
            }
        }
    }

    override fun onResume() {
        super.onResume()
        //更新最近
        updateRecent()
        //更新自定义
        updateMeetingList()

        queryImageJob = lifecycleScope.launch(Dispatchers.IO) {
            while (true) {
                delay(3000)
                if (!isReceiving.get() && photoVM.isNetworking.get()) {
                    photoVM.queryUnReceived()
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        recentAdapter.cancelTimeCount()
        queryImageJob?.cancel()
    }

}